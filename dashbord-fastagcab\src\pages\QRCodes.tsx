import React, { useState, useRef, useEffect } from "react";
import {
  Plus,
  Download,
  Search,
  Upload,
  Filter,
  X,
  Archive,
  RefreshCw,
  AlertCircle,
  QrCode as QrCodeIcon,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import QRCode from "react-qr-code";
import * as XLSX from "xlsx";
import J<PERSON><PERSON><PERSON> from "jszip";
import { qrAPI } from "../lib/api";
import toast from "react-hot-toast";

interface QRCodeData {
  _id?: string;
  id?: number;
  sNo?: number;
  productName: string;
  points: number;
  createdAt?: string;
  createdDate?: string;
  value: number;
  qrCode: string;
  status: string;
  productSize: string;
  createdBy?: string;
  redeemedBy?: string;
  redeemedAt?: string;
}

interface ProductOption {
  size: string;
  points: number;
}

const QRCodes: React.FC = () => {
  const productOptions: ProductOption[] = [
    { size: "0.75 mm", points: 5 },
    { size: "1.0 mm", points: 10 },
    { size: "1.5 mm", points: 15 },
    { size: "2.5 mm", points: 25 },
    { size: "4.0 mm", points: 40 },
    { size: "6.0 mm", points: 60 },
    { size: "10.0 mm", points: 100 },
    { size: "DDH 0.75 mm", points: 5 },
    { size: "DDH 1.0 mm", points: 5 },
    { size: "DDH 1.5 mm", points: 10 },
    { size: "DDH 2.5 mm", points: 15 },
    { size: "DDH 4.0 mm", points: 20 },
  ];

  const [qrCodes, setQrCodes] = useState<QRCodeData[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportProgress, setExportProgress] = useState({
    current: 0,
    total: 0,
  });
  const [stats, setStats] = useState({
    totalCodes: 0,
    notRedeemed: 0,
    redeemed: 0,
    totalPoints: 0,
  });
  const [filters, setFilters] = useState({
    productSize: "all",
    status: "all",
    dateRange: "all",
    pointsRange: "all",
  });
  const [formData, setFormData] = useState({
    productSize: "0.75 mm",
    quantity: "1",
    downloadAfterGeneration: false,
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper functions for UI
  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "redeemed":
        return "bg-[#1ca63a] text-white"; // Green
      case "not redeemed":
        return "bg-[#7e8689] text-white"; // Grey
      case "expired":
        return "bg-[#df5921] text-white"; // Orange
      default:
        return "bg-[#7e8689] text-white"; // Grey
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "redeemed":
        return <CheckCircle className="h-4 w-4" />;
      case "not redeemed":
        return <Clock className="h-4 w-4" />;
      case "expired":
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  // Fetch QR codes from server
  useEffect(() => {
    fetchQRCodes();
    fetchStats();
  }, []);

  // Fetch QR codes based on filters
  useEffect(() => {
    if (showFilters) {
      fetchQRCodes();
    }
  }, [filters]);

  const fetchQRCodes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append("limit", "10000");
      params.append("page", "1");

      if (searchTerm) params.append("search", searchTerm);
      if (filters.status !== "all") params.append("status", filters.status);
      if (filters.productSize !== "all")
        params.append("productSize", filters.productSize);
      if (filters.pointsRange !== "all")
        params.append("pointsRange", filters.pointsRange);
      if (filters.dateRange !== "all")
        params.append("dateRange", filters.dateRange);

      const apiParams: any = {
        limit: 10000,
        page: 1,
      };

      if (searchTerm) apiParams.search = searchTerm;
      if (filters.status !== "all") apiParams.status = filters.status;
      if (filters.productSize !== "all") apiParams.productSize = filters.productSize;
      if (filters.pointsRange !== "all") apiParams.pointsRange = filters.pointsRange;
      if (filters.dateRange !== "all") apiParams.dateRange = filters.dateRange;

      const response = await qrAPI.getAll(apiParams);

      if (response && response.qrCodes) {
        const qrCodesWithSNo = response.qrCodes.map(
          (code: QRCodeData, index: number) => ({
            ...code,
            sNo: index + 1,
            createdDate: code.createdAt
              ? new Date(code.createdAt)
                  .toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                  .replace(",", "")
              : "",
          })
        );
        setQrCodes(qrCodesWithSNo);
        toast.success("QR codes loaded successfully");
      } else {
        setQrCodes([]);
      }
    } catch (error: any) {
      console.error("Error fetching QR codes:", error);
      const message = error.response?.data?.message || "Error fetching QR codes. Please try again.";
      setError(message);
      toast.error(message);

      if (error.response?.status === 401) {
        localStorage.removeItem("admin_token");
        window.location.href = "/login";
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await qrAPI.getStats();
      if (response) {
        setStats(response);
      }
    } catch (error: any) {
      console.error("Error fetching QR code stats:", error);
      toast.error("Failed to load QR code statistics");
    }
  };

  const generateRandomCode = (prefix: string = "FAS"): string => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = prefix;
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const quantity = parseInt(formData.quantity);

    try {
      setLoading(true);
      const response = await qrAPI.create({
        productSize: formData.productSize,
        quantity,
      });

      if (response && response.qrCodes) {
        toast.success(`Successfully generated ${quantity} QR codes!`);

        if (formData.downloadAfterGeneration) {
          await downloadGeneratedQRImages(response.qrCodes);
        }

        await fetchQRCodes();
        await fetchStats();
        setShowForm(false);
        setFormData({
          productSize: "0.75 mm",
          quantity: "1",
          downloadAfterGeneration: false,
        });
      }
    } catch (error: any) {
      console.error("Error generating QR codes:", error);
      const message = error.response?.data?.message || "Error generating QR codes. Please try again.";
      toast.error(message);
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      product Spielberg: The Movie
      productSize: "all",
      status: "all",
      dateRange: "all",
      pointsRange: "all",
    });
    setTimeout(() => fetchQRCodes(), 100);
  };

  const handleSearch = () => {
    fetchQRCodes();
  };

  const handleQRImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileImport = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        setLoading(true);
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        const importedCodes = jsonData.map((row: any) => ({
          productName:
            row["Product Name"] || `FASTAG CAB WIRE ${row["Size"] || "1.0 mm"}`,
          points: row["Points"] || 10,
          value: row["Value"] || row["Points"] || 10,
          qrCode: row["QR Code"] || generateRandomCode(),
          status: row["Status"] || "Not Redeem",
          productSize: row["Size"] || "1.0 mm",
        }));

        const response = await qrAPI.bulkImport(importedCodes);

        if (response && response.qrCodes) {
          toast.success(
            `Successfully imported ${response.qrCodes.length} QR codes!`
          );
          await fetchQRCodes();
          await fetchStats();
        }
      } catch (error: any) {
        console.error("Error importing file:", error);
        const message = error.response?.data?.message || "Error importing file. Please check the file format.";
        toast.error(message);
        setError(message);
      } finally {
        setLoading(false);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const exportToExcel = () => {
    const exportData = filteredCodes.map((code) => ({
      "S.No": code.sNo,
      "Product Name": code.productName,
      Points: code.points,
      "Created Date": code.createdDate,
      Value: code.value,
      "QR Code": code.qrCode,
      Status: code.status,
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "QR Codes");
    XLSX.writeFile(wb, "qr-codes.xlsx");
  };

  const exportQRImage = (qrCode: string, productName: string) => {
    import("qrcode")
      .then((QRCodeLib) => {
        const canvas = document.createElement("canvas");
        const size = 512;
        canvas.width = size;
        canvas.height = size;

        QRCodeLib.toCanvas(
          canvas,
          qrCode,
          {
            width: size,
            margin: 2,
            color: {
              dark: "#000000",
              light: "#ffffff",
            },
          },
          (error) => {
            if (error) {
              console.error("Error generating QR code:", error);
              toast.error("Error generating QR code image");
              return;
            }

            canvas.toBlob((blob) => {
              if (!blob) {
                toast.error("Error creating image");
                return;
              }

              const url = URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.href = url;
              link.download = `QR_${qrCode}_${productName.replace(
                /[^a-zA-Z0-9]/g,
                "_"
              )}.png`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(url);
            }, "image/png");
          }
        );
      })
      .catch((error) => {
        console.error("Error loading qrcode library:", error);
        toast.error("Error loading QR code library");
      });
  };

  const exportAllQRImages = async () => {
    if (filteredCodes.length === 0) {
      toast.error("No QR codes to export");
      return;
    }

    try {
      setLoading(true);
      setExportProgress({ current: 0, total: filteredCodes.length });
      const zip = new JSZip();
      const QRCodeLib = await import("qrcode");

      const batchSize = 10;
      let processedCount = 0;

      for (let i = 0; i < filteredCodes.length; i += batchSize) {
        const batch = filteredCodes.slice(i, i + batchSize);

        await Promise.all(
          batch.map(async (code) => {
            try {
              const dataUrl = await QRCodeLib.toDataURL(code.qrCode, {
                width: 512,
                margin: 2,
                color: {
                  dark: "#000000",
                  light: "#ffffff",
                },
              });

              const response = await fetch(dataUrl);
              const blob = await response.blob();

              const fileName = `QR_${code.qrCode}_${code.productName.replace(
                /[^a-zA-Z0-9]/g,
                "_"
              )}.png`;

              zip.file(fileName, blob);
              processedCount++;
            } catch (error) {
              console.error(`Error processing QR code ${code.qrCode}:`, error);
            }
          })
        );

        setExportProgress({
          current: processedCount,
          total: filteredCodes.length,
        });
      }

      const zipBlob = await zip.generateAsync({ type: "blob" });
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `QR_Codes_Export_${new Date().toISOString().split("T")[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Successfully exported ${processedCount} QR code images!`);
    } catch (error) {
      console.error("Error exporting QR codes:", error);
      toast.error("Error exporting QR codes. Please try again.");
    } finally {
      setLoading(false);
      setExportProgress({ current: 0, total: 0 });
    }
  };

  const downloadGeneratedQRImages = async (generatedCodes: QRCodeData[]) => {
    if (generatedCodes.length === 0) return;

    try {
      setLoading(true);
      setExportProgress({ current: 0, total: generatedCodes.length });

      if (generatedCodes.length === 1) {
        const code = generatedCodes[0];
        exportQRImage(code.qrCode, code.productName);
      } else {
        const zip = new JSZip();
        const QRCodeLib = await import("qrcode");

        let processedCount = 0;

        for (const code of generatedCodes) {
          try {
            const dataUrl = await QRCodeLib.toDataURL(code.qrCode, {
              width: 512,
              margin: 2,
              color: {
                dark: "#000000",
                light: "#ffffff",
              },
            });

            const response = await fetch(dataUrl);
            const blob = await response.blob();

            const fileName = `QR_${code.qrCode}_${code.productName.replace(
              /[^a-zA-Z0-9]/g,
              "_"
            )}.png`;

            zip.file(fileName, blob);
            processedCount++;

            setExportProgress({
              current: processedCount,
              total: generatedCodes.length,
            });
          } catch (error) {
            console.error(`Error processing QR code ${code.qrCode}:`, error);
          }
        }

        const zipBlob = await zip.generateAsync({ type: "blob" });
        const url = URL.createObjectURL(zipBlob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `Generated_QR_Codes_${new Date().toISOString().split("T")[0]}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error downloading generated QR codes:", error);
      toast.error("Error downloading QR codes. Please try again.");
    } finally {
      setLoading(false);
      setExportProgress({ current: 0, total: 0 });
    }
  };

  const filteredCodes = qrCodes;

  // Loading state
  if (loading && qrCodes.length === 0) {
    return (
      <div className="space-y-6 bg-[#ffffff] dark:bg-[#1A1A1A] p-6" aria-busy="true">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-[#1A1A1A] dark:text-[#ffffff]">
              QR Code Generator
            </h1>
            <p className="text-[#7e8689] mt-1">
              Generate and manage QR codes for your products
            </p>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1ca63a]"></div>
          <p className="text-[#7e8689]">Loading QR codes...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && qrCodes.length === 0) {
    return (
      <div className="space-y-6 bg-[#ffffff] dark:bg-[#1A1A1A] p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-[#1A1A1A] dark:text-[#ffffff]">
              QR Code Generator
            </h1>
            <p className="text-[#7e8689] mt-1">
              Generate and manage QR codes for your products
            </p>
          </div>
          <button
            onClick={() => {
              setError(null);
              fetchQRCodes();
              fetchStats();
            }}
            className="flex items-center gap-2 bg-[#1ca63a] text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#1ca63a]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2 disabled:opacity-50"
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </button>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-[#df5921]" />
          <div className="text-center">
            <p className="text-[#1A1A1A] dark:text-[#ffffff] font-medium">
              Failed to load QR codes
            </p>
            <p className="text-[#7e8689] mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-[#ffffff] dark:bg-[#1A1A1A] p-6">
      {loading && (
        <div
          className="fixed inset-0 bg-[#1A1A1A]/50 flex items-center justify-center z-50"
          aria-label="Loading"
        >
          <div className="bg-[#ffffff] dark:bg-[#2A2A2A] p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <p className="text-lg font-semibold text-[#1A1A1A] dark:text-[#ffffff] mb-4">
              {exportProgress.total > 0
                ? "Exporting QR Images..."
                : "Loading..."}
            </p>
            {exportProgress.total > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-[#7e8689]">
                  <span>Progress</span>
                  <span>
                    {exportProgress.current} / {exportProgress.total}
                  </span>
                </div>
                <div className="w-full bg-[#7e8689]/20 rounded-full h-2">
                  <div
                    className="bg-[#1ca63a] h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        exportProgress.total > 0
                          ? (exportProgress.current / exportProgress.total) * 100
                          : 0
                      }%`,
                    }}
                  ></div>
                </div>
                <p className="text-xs text-[#7e8689] text-center">
                  Please wait while we generate your QR code images...
                </p>
              </div>
            )}
          </div>
        </div>
      )}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-[#1A1A1A] dark:text-[#ffffff]">
            QR Code Generator
          </h1>
          <p className="text-[#7e8689] mt-1">
            Generate and manage QR codes for your products
          </p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => {
              fetchQRCodes();
              fetchStats();
            }}
            disabled={loading}
            className="flex items-center gap-2 bg-[#7e8689] text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#7e8689]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            <span>Refresh</span>
          </button>
          <button
            onClick={handleQRImport}
            className="flex items-center gap-2 bg-[#df5921] text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#df5921]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
          >
            <Upload className="h-4 w-4" />
            <span>Import QR Codes</span>
          </button>
          <button
            onClick={exportToExcel}
            className="flex items-center gap-2 bg-[#1ca63a] text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#1ca63a]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
          >
            <Download className="h-4 w-4" />
            <span>Export Excel</span>
          </button>
          <button
            onClick={exportAllQRImages}
            className="flex items-center gap-2 bg-[#df5921] text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#df5921]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2 disabled:opacity-50"
            disabled={loading || filteredCodes.length === 0}
          >
            <Archive className="h-4 w-4" />
            <span>Download All QR Images</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="flex items-center gap-2 bg-[#1ca63a] text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#1ca63a]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
          >
            <Plus className="h-4 w-4" />
            <span>Generate QR Codes</span>
          </button>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 ${
              showFilters ? "bg-[#1ca63a]/90" : "bg-[#1ca63a]"
            } text-[#ffffff] px-4 py-2 rounded-md hover:bg-[#1ca63a]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2`}
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </button>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileImport}
          className="hidden"
          accept=".xlsx,.xls,.csv"
          aria-label="Upload QR codes file"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {[
          { label: "Total QR Codes", value: stats.totalCodes, color: "text-[#1A1A1A]" },
          { label: "Not Redeemed", value: stats.notRedeemed, color: "text-[#1ca63a]" },
          { label: "Redeemed", value: stats.redeemed, color: "text-[#df5921]" },
          { label: "Total Points", value: stats.totalPoints, color: "text-[#1ca63a]" },
        ].map((stat, index) => (
          <div
            key={index}
            className="bg-[#ffffff] dark:bg-[#2A2A2A] rounded-lg shadow-sm border border-[#7e8689]/20 p-4"
          >
            <p className="text-sm font-medium text-[#7e8689]">{stat.label}</p>
            <p className={`text-2xl font-bold ${stat.color} dark:text-[#ffffff]`}>
              {stat.value}
            </p>
          </div>
        ))}
      </div>

      {/* Generate Form */}
      {showForm && (
        <div
          className="bg-[#ffffff] dark:bg-[#2A2A2A] rounded-lg shadow-sm border border-[#7e8689]/20"
          aria-modal="true"
        >
          <div className="px-6 py-4 border-b border-[#7e8689]/20">
            <h2 className="text-lg font-semibold text-[#1A1A1A] dark:text-[#ffffff]">
              Generate QR Codes
            </h2>
          </div>
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="bg-[#1ca63a]/5 border border-[#1ca63a]/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-3">
                Available Point Options
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-xs font-medium text-[#7e8689] mb-2">
                    Fastagcab Options
                  </h4>
                  <div className="space-y-1 text-xs text-[#7e8689]">
                    <div>0.75mm: 5 pts • 1.0mm: 10 pts • 1.5mm: 15 pts</div>
                    <div>
                      2.5mm: 25 pts • 4.0mm: 40 pts • 6.0mm: 60 pts • 10.0mm:
                      100 pts
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="text-xs font-medium text-[#7e8689] mb-2">
                    DDH Options
                  </h4>
                  <div className="space-y-1 text-xs text-[#7e8689]">
                    <div>DDH 0.75mm: 5 pts • DDH 1.0mm: 5 pts</div>
                    <div>
                      DDH 1.5mm: 10 pts • DDH 2.5mm: 15 pts • DDH 4.0mm: 20 pts
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="productSize"
                  className="block text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-2"
                >
                  Product Size *
                </label>
                <select
                  id="productSize"
                  name="productSize"
                  value={formData.productSize}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
                >
                  <optgroup label="Fastagcab Options">
                    {productOptions
                      .filter((option) => !option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="DDH Options">
                    {productOptions
                      .filter((option) => option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>
              <div>
                <label
                  htmlFor="quantity"
                  className="block text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-2"
                >
                  Quantity *
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  required
                  min="1"
                  max="100"
                  className="w-full px-3 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
                  placeholder="Number of QR codes to generate"
                />
              </div>
            </div>

            <div className="bg-[#1ca63a]/5 border border-[#1ca63a]/20 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="downloadAfterGeneration"
                  name="downloadAfterGeneration"
                  checked={formData.downloadAfterGeneration}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-[#1ca63a] focus:ring-[#1ca63a] border-[#7e8689] rounded"
                />
                <label
                  htmlFor="downloadAfterGeneration"
                  className="text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff]"
                >
                  Automatically download QR code images after generation
                </label>
              </div>
              <p className="text-xs text-[#7e8689] mt-2 ml-6">
                When enabled, QR code images will be downloaded as PNG files (single QR code) or a ZIP file (multiple QR codes).
              </p>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="px-6 py-2 border border-[#7e8689] text-[#1A1A1A] dark:text-[#ffffff] rounded-md hover:bg-[#7e8689]/10 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-[#1ca63a] text-[#ffffff] rounded-md hover:bg-[#1ca63a]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
              >
                Generate QR Codes
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-[#ffffff] dark:bg-[#2A2A2A] rounded-lg shadow-sm border border-[#7e8689]/20 p-6">
        <div className="flex gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#7e8689] h-5 w-5" />
            <input
              type="text"
              placeholder="Search QR codes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="w-full pl-10 pr-4 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
              aria-label="Search QR codes"
            />
          </div>
          <button
            onClick={handleSearch}
            className="px-6 py-2 bg-[#1ca63a] text-[#ffffff] rounded-md hover:bg-[#1ca63a]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
          >
            <Search className="h-4 w-4 mr-2 inline" />
            Search
          </button>
          <button
            onClick={() => {
              setSearchTerm("");
              clearFilters();
              fetchQRCodes();
            }}
            className="px-6 py-2 bg-[#df5921] text-[#ffffff] rounded-md hover:bg-[#df592]1/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2"
          >
            Show All QR Codes
          </button>
        </div>

        {showFilters && (
          <div className="mt-4 border-t border-[#7e8689]/20 pt-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-[#1A1A1A] dark:text-[#ffffff]">
                Filter QR Codes
              </h3>
              <button
                onClick={clearFilters}
                className="text-sm text-[#1ca63a] hover:text-[#1ca63a]/80 flex items-center"
              >
                <X className="h-4 w-4 mr-1" />
                Clear Filters
              </button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label
                  htmlFor="productSizeFilter"
                  className="block text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-1"
                >
                  Product Size
                </label>
                <select
                  id="productSizeFilter"
                  name="productSize"
                  value={filters.productSize}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
                >
                  <option value="all">All Sizes</option>
                  <optgroup label="Fastagcab Options">
                    {productOptions
                      .filter((option) => !option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="DDH Options">
                    {productOptions
                      .filter((option) => option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>
              <div>
                <label
                  htmlFor="statusFilter"
                  className="block text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-1"
                >
                  Status
                </label>
                <select
                  id="statusFilter"
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="Not Redeem">Not Redeemed</option>
                  <option value="Redeemed">Redeemed</option>
                </select>
              </div>
              <div>
                <label
                  htmlFor="pointsRangeFilter"
                  className="block text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-1"
                >
                  Points Range
                </label>
                <select
                  id="pointsRangeFilter"
                  name="pointsRange"
                  value={filters.pointsRange}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
                >
                  <option value="all">All Points</option>
                  <option value="low">Low (≤ 15)</option>
                  <option value="medium">Medium (16-40)</option>
                  <option value="high">High (> 40)</option>
                </select>
              </div>
              <div>
                <label
                  htmlFor="dateRangeFilter"
                  className="block text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff] mb-1"
                >
                  Date Range
                </label>
                <select
                  id="dateRangeFilter"
                  name="dateRange"
                  value={filters.dateRange}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-[#7e8689] rounded-md bg-[#ffffff] dark:bg-[#2A2A2A] text-[#1A1A1A] dark:text-[#ffffff] focus:ring-2 focus:ring-[#1ca63a] focus:border-transparent"
                >
                  <option value="all">All Dates</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* QR Codes Table */}
      <div className="bg-[#ffffff] dark:bg-[#2A2A2A] rounded-lg shadow-sm border border-[#7e8689]/20 overflow-hidden">
        <div className="flex justify-between items-center px-6 py-4 border-b border-[#7e8689]/20">
          <h3 className="text-lg font-medium text-[#1A1A1A] dark:text-[#ffffff]">
            QR Code List
          </h3>
          <p className="text-sm text-[#7e8689]">
            Showing {filteredCodes.length} of {stats.totalCodes} QR codes
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-[#7e8689]/20">
            <thead className="bg-[#1ca63a]">
              <tr>
                {[
                  "S.No",
                  "Product Name",
                  "Points",
                  "Created Date",
                  "Value",
                  "QR Code",
                  "Status",
                  "QR Image",
                  "Actions",
                ].map((header) => (
                  <th
                    key={header}
                    className="px-6 py-3 text-left text-xs font-medium text-[#ffffff] uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-[#7e8689]/20">
              {filteredCodes.map((code) => (
                <tr
                  key={code._id || code.id}
                  className="hover:bg-[#7e8689]/5"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#1A1A1A] dark:text-[#ffffff]">
                    {code.sNo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#1A1A1A] dark:text-[#ffffff]">
                    {code.productName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#1A1A1A] dark:text-[#ffffff]">
                    {code.points}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#7e8689]">
                    {code.createdDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#1A1A1A] dark:text-[#ffffff]">
                    {code.value}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[#1A1A1A] dark:text-[#ffffff]">
                    {code.qrCode}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeVariant(
                        code.status
                      )}`}
                    >
                      {getStatusIcon(code.status)}
                      <span className="ml-1">{code.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-16 h-16">
                      <QRCode
                        value={code.qrCode}
                        size={64}
                        style={{
                          height: "auto",
                          maxWidth: "100%",
                          width: "100%",
                        }}
                      />
                    </div>
                  </td>
                  <td className="px-6 py4 whitespace-nowrap">
                    <button
                      onClick={() =>
                        exportQRImage(code.qrCode, code.productName)
                      }
                      className="flex items-center gap-1 bg-[#df5921] text-[#ffffff] px-3 py-1 rounded-md hover:bg-[#df5921]/90 focus:ring-2 focus:ring-[#1ca63a] focus:ring-offset-2 text-xs"
                      aria-label={`Download QR code image for ${code.productName}`}
                    >
                      <Download className="h-3 w-3" />
                      <span>Download</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default QRCodes;