// import express from 'express';
// import {
//   getAllRechargesForAdmin,
//   updateRechargeStatusByAdmin
// } from '../controllers/rechargeAdminController.js';
// import { verifyAdminToken } from '../middlewares/authMiddleware.js';

// const router = express.Router();

// // GET all recharges for admin
// router.get('/', verifyAdminToken, getAllRechargesForAdmin);

// // PATCH update recharge status
// router.patch('/admin/:id', verifyAdminToken, updateRechargeStatusByAdmin);

// export default router;
