import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from './Models/user.js';

// Load environment variables
dotenv.config();

const updateTestUserPassword = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Find the test user
    const testUser = await User.findOne({ phoneNumber: '8959305284' });
    if (!testUser) {
      console.log('❌ Test user not found with phone number: 8959305284');
      process.exit(1);
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash('000000', 12);

    // Update the user's password
    testUser.password = hashedPassword;
    await testUser.save();

    console.log('✅ Test user password updated successfully!');
    console.log('   Login credentials:');
    console.log('   Phone: 8959305284');
    console.log('   Password: 000000');
    console.log('   Role:', testUser.role);

  } catch (error) {
    console.error('❌ Error updating test user password:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
    process.exit(0);
  }
};

updateTestUserPassword();
