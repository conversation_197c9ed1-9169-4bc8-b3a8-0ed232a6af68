import AsyncStorage from '@react-native-async-storage/async-storage';

interface InteraktWhatsAppResponse {
  success: boolean;
  message: string;
  data?: any;
}

class InteraktWhatsAppService {
  // Interakt WhatsApp API configuration
  private baseURL = 'https://api.interakt.ai';
  private apiKey = 'NThLVHR6SGgwNW1pUXZlZmpWTS05N2c3d2czeUl4X3owX0dtZTlxVmNfMDo='; // Use the working API key
  private templateId = 'otp_verification';

  // Generate random OTP
  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Store OTP for verification
  private async storeOTP(phoneNumber: string, otp: string): Promise<void> {
    const expiresAt = Date.now() + (30 * 60 * 1000); // 30 minutes (increased from 15)
    const otpData = {
      otp,
      timestamp: Date.now(),
      expiresAt
    };
    await AsyncStorage.setItem(`interakt_whatsapp_otp_${phoneNumber}`, JSON.stringify(otpData));
    console.log('🔢 Interakt WhatsApp Service: OTP stored for verification:', otp);
    console.log('⏰ Interakt WhatsApp Service: OTP expires at:', new Date(expiresAt).toLocaleString());
  }

  // Send WhatsApp OTP using Interakt API (matching HTML example)
  async sendOTP(phoneNumber: string): Promise<InteraktWhatsAppResponse> {
    try {
      console.log('📱 Interakt WhatsApp Service: Starting OTP send for:', phoneNumber);

      // Generate OTP
      const otp = this.generateOTP();
      console.log('🔢 Interakt WhatsApp Service: Generated OTP:', otp);

      // Format phone number (remove + prefix)
      const formattedPhone = phoneNumber.replace(/^\+/, '');
      console.log('📞 Interakt WhatsApp Service: Formatted phone:', formattedPhone);

      // Use Template message like in HTML example
      const messageData = {
        countryCode: "+91",
        phoneNumber: formattedPhone,
        callbackData: "registration-otp",
        type: "Template",
        template: {
          name: "otp_verification", // Ensure this template exists in your Interakt account
          languageCode: "en",
          bodyValues: [otp],
          buttonValues: { 0: [otp] }
        }
      };

      console.log('🌐 Interakt WhatsApp Service: Making API request');
      console.log('📤 Request data:', JSON.stringify(messageData, null, 2));

      const response = await fetch(`${this.baseURL}/v1/public/message/`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      });

      const responseData = await response.json();
      console.log('📥 Interakt API Response:', JSON.stringify(responseData, null, 2));

      if (response.ok && responseData.result) {
        // Store OTP for verification
        await this.storeOTP(phoneNumber, otp);

        return {
          success: true,
          message: 'OTP sent successfully to your WhatsApp',
          data: {
            messageId: responseData.result.messageId,
            to: formattedPhone,
            otp: otp
          }
        };
      } else {
        const errorMessage = responseData.message || 'Failed to send WhatsApp message via Interakt';
        console.error('❌ Interakt WhatsApp API Error:', errorMessage);

        return {
          success: false,
          message: errorMessage,
          data: responseData
        };
      }
    } catch (error) {
      console.error('🚨 Interakt WhatsApp send error:', error);
      return {
        success: false,
        message: 'Network error while sending OTP'
      };
    }
  }

  // Verify OTP
  async verifyOTP(phoneNumber: string, enteredOTP: string): Promise<InteraktWhatsAppResponse> {
    try {
      console.log('🔍 Interakt WhatsApp Service: Verifying OTP for:', phoneNumber);
      console.log('🔢 Interakt WhatsApp Service: Entered OTP:', enteredOTP);

      const storedData = await AsyncStorage.getItem(`interakt_whatsapp_otp_${phoneNumber}`);

      if (!storedData) {
        console.log('❌ Interakt WhatsApp Service: No OTP found in storage');
        return {
          success: false,
          message: 'OTP not found. Please request a new OTP.'
        };
      }

      const { otp, expiresAt } = JSON.parse(storedData);
      console.log('🔢 Interakt WhatsApp Service: Stored OTP:', otp);
      console.log('🔢 Interakt WhatsApp Service: Entered OTP:', enteredOTP);
      console.log('⏰ Interakt WhatsApp Service: Current time:', new Date().toLocaleString());
      console.log('⏰ Interakt WhatsApp Service: Expires at:', new Date(expiresAt).toLocaleString());
      console.log('⏰ Interakt WhatsApp Service: Time remaining:', Math.round((expiresAt - Date.now()) / 1000 / 60), 'minutes');

      // Check if OTP is expired
      if (Date.now() > expiresAt) {
        await AsyncStorage.removeItem(`interakt_whatsapp_otp_${phoneNumber}`);
        console.log('⏰ Interakt WhatsApp Service: OTP expired');
        return {
          success: false,
          message: 'OTP has expired. Please request a new OTP.'
        };
      }

      // Verify OTP
      if (otp === enteredOTP) {
        await AsyncStorage.removeItem(`interakt_whatsapp_otp_${phoneNumber}`);
        console.log('✅ Interakt WhatsApp Service: OTP verified successfully');
        return {
          success: true,
          message: 'OTP verified successfully'
        };
      } else {
        console.log('❌ Interakt WhatsApp Service: OTP mismatch');
        return {
          success: false,
          message: 'Invalid OTP. Please try again.'
        };
      }
    } catch (error) {
      console.error('🚨 Interakt WhatsApp OTP verification error:', error);
      return {
        success: false,
        message: 'Error verifying OTP. Please try again.'
      };
    }
  }

  // Resend OTP
  async resendOTP(phoneNumber: string): Promise<InteraktWhatsAppResponse> {
    console.log('🔄 Interakt WhatsApp Service: Resending OTP for:', phoneNumber);
    
    // Clear existing OTP
    await AsyncStorage.removeItem(`interakt_whatsapp_otp_${phoneNumber}`);

    // Send new OTP
    return this.sendOTP(phoneNumber);
  }
}

export default new InteraktWhatsAppService();
