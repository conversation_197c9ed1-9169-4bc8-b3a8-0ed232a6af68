import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';

interface BasicPhotoUploadProps {
  title: string;
  subtitle?: string;
  required?: boolean;
  value: string;
  onPhotoSelected: (uri: string) => void;
  error?: string;
  cameraOnly?: boolean;
}

export default function BasicPhotoUpload({
  title,
  subtitle,
  required = false,
  value,
  onPhotoSelected,
  error,
  cameraOnly = false,
}: BasicPhotoUploadProps) {
  const [isLoading, setIsLoading] = useState(false);

  const selectPhoto = async () => {
    setIsLoading(true);
    try {
      console.log('📸 Basic photo selection started...');

      if (cameraOnly) {
        // Camera only mode
        console.log('📸 Camera only mode - requesting camera permission...');
        const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

        if (!cameraPermission.granted) {
          Alert.alert('Permission Required', 'Camera permission is required to take photos.');
          return;
        }

        const result = await ImagePicker.launchCameraAsync({
          mediaTypes: ['images'],
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });

        console.log('📸 Camera result:', result);

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const uri = result.assets[0].uri;
          console.log('📸 Camera photo URI:', uri);
          onPhotoSelected(uri);
          Alert.alert('Success', 'Photo captured!');
        }
      } else {
        // Gallery selection
        console.log('📸 Gallery mode - requesting media library permission...');
        const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (!mediaPermission.granted) {
          Alert.alert('Permission Required', 'Media library permission is required to select photos.');
          return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ['images'],
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });

        console.log('📸 Gallery result:', result);

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const uri = result.assets[0].uri;
          console.log('📸 Gallery photo URI:', uri);
          onPhotoSelected(uri);
          Alert.alert('Success', 'Photo selected!');
        }
      }
    } catch (error: any) {
      console.error('📸 Basic photo error:', error);
      Alert.alert('Error', `Photo selection failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const removePhoto = () => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => onPhotoSelected('') },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {title}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      <TouchableOpacity
        style={[styles.uploadContainer, error ? styles.uploadError : null]}
        onPress={selectPhoto}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="large" color="#007AFF" />
        ) : value ? (
          <View style={styles.imageContainer}>
            <Image source={{ uri: value }} style={styles.image} />
            <TouchableOpacity style={styles.removeButton} onPress={removePhoto}>
              <Ionicons name="close-circle" size={24} color="#FF3B30" />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.placeholderContainer}>
            <Ionicons name="camera" size={40} color="#007AFF" />
            <Text style={styles.placeholderText}>
              {cameraOnly ? 'Tap to take photo' : 'Tap to select photo'}
            </Text>
            {!cameraOnly && <Text style={styles.placeholderSubtext}>Camera or Gallery</Text>}
          </View>
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  required: {
    color: '#FF3B30',
  },
  subtitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  uploadContainer: {
    borderWidth: 2,
    borderColor: '#E5E5E7',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
    backgroundColor: '#F8F9FA',
  },
  uploadError: {
    borderColor: '#FF3B30',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: '35%',
    backgroundColor: 'white',
    borderRadius: 12,
  },
  placeholderContainer: {
    alignItems: 'center',
  },
  placeholderText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  placeholderSubtext: {
    marginTop: 4,
    fontSize: 12,
    color: '#999',
  },
  errorText: {
    marginTop: 4,
    fontSize: 12,
    color: '#FF3B30',
  },
});
