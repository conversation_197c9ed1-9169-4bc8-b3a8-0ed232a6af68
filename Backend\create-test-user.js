import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from './Models/user.js';

// Load environment variables
dotenv.config();

const createTestUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Check if test user already exists
    const existingUser = await User.findOne({ phoneNumber: '8959305284' });
    if (existingUser) {
      console.log('❌ Test user already exists with phone number: 8959305284');
      console.log('   You can use these credentials to login:');
      console.log('   Phone: 8959305284');
      console.log('   Password: 000000');
      process.exit(0);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('000000', 12);

    // Create test user
    const testUser = new User({
      fullName: 'Test User',
      phoneNumber: '8959305284',
      password: hashedPassword,
      email: '<EMAIL>',
      role: 'Electrician',
      status: 'approved',
      isVerified: true,
      monthlyPoints: 500,
      yearlyPoints: 1200,
      dateOfBirth: new Date('1990-01-01'),
      age: 34,
      adharNumber: '123456789012',
      panCardNumber: '**********',
      pinCode: '110001',
      state: 'Delhi',
      city: 'New Delhi',
      address: 'Test Address, New Delhi',
      dealerCode: 'TEST001'
    });

    await testUser.save();
    console.log('✅ Test user created successfully!');
    console.log('   Login credentials:');
    console.log('   Phone: 8959305284');
    console.log('   Password: securePass123');
    console.log('   Role: Electrician');

  } catch (error) {
    console.error('❌ Error creating test user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
    process.exit(0);
  }
};

createTestUser();
